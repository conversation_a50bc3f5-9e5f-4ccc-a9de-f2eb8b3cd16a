import React, { Suspense, useState, useEffect } from 'react'
import { Canvas } from '@react-three/fiber'
import { motion, AnimatePresence } from 'framer-motion'
import Navigation from './components/Navigation'
import HeroNew from './components/HeroNew'
import AboutNew from './components/AboutNew'
import Projects from './components/Projects'
// import Experience from './components/Experience'
import Skills from './components/Skills'
import AIPlayground from './components/AIPlayground'
import Contact from './components/Contact'
import LoadingScreen from './components/LoadingScreen'

function App() {
  const [isLoading, setIsLoading] = useState(true)
  const [currentSection, setCurrentSection] = useState('hero')

  useEffect(() => {
    // Simulate loading time for 3D assets
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 3000)

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      const sections = ['hero', 'about', 'projects', 'experience', 'skills', 'ai-playground', 'contact']
      const scrollPosition = window.scrollY + window.innerHeight / 2

      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const { offsetTop, offsetHeight } = element
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setCurrentSection(section)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <div className="App">
      <Navigation currentSection={currentSection} />

      <main>
        <section id="hero">
          <HeroNew />
        </section>

        <section id="about">
          <AboutNew />
        </section>

        <section id="projects">
          <Projects />
        </section>

        {/* <section id="experience">
          <Experience />
        </section> */}

        <section id="skills">
          <Skills />
        </section>

        <section id="ai-playground">
          <AIPlayground />
        </section>

        <section id="contact">
          <Contact />
        </section>
      </main>

      {/* Background Canvas for 3D effects */}
      <div className="fixed inset-0 -z-10">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 75 }}
          style={{ background: 'transparent' }}
        >
          <Suspense fallback={null}>
            {/* Background 3D elements will be added here */}
          </Suspense>
        </Canvas>
      </div>
    </div>
  )
}

export default App
