import React from 'react'
import { motion } from 'framer-motion'
import { Code, Database, Brain, Cpu, Zap, Globe, Wrench, BookOpen } from 'lucide-react'

const Skills: React.FC = () => {
  const skillCategories = [
    {
      title: "Programming Languages",
      icon: Code,
      color: "from-blue-400 to-cyan-400",
      skills: [
        { name: "Python", level: 95, color: "bg-yellow-500" },
        { name: "C++", level: 90, color: "bg-blue-500" },
        { name: "JavaScript", level: 85, color: "bg-yellow-400" },
        { name: "P<PERSON>", level: 80, color: "bg-purple-500" },
        { name: "HTML/CSS", level: 90, color: "bg-orange-500" }
      ]
    },
    {
      title: "AI/ML & Data Science",
      icon: Brain,
      color: "from-purple-400 to-pink-400",
      skills: [
        { name: "Machine Learning", level: 92, color: "bg-green-500" },
        { name: "Tensor<PERSON><PERSON>", level: 88, color: "bg-orange-500" },
        { name: "<PERSON>yTor<PERSON>", level: 85, color: "bg-red-500" },
        { name: "Computer Vision", level: 87, color: "bg-blue-500" },
        { name: "NLP", level: 83, color: "bg-purple-500" },
        { name: "Reinforcement Learning", level: 80, color: "bg-indigo-500" }
      ]
    },
    {
      title: "Robotics & Hardware",
      icon: Cpu,
      color: "from-green-400 to-emerald-400",
      skills: [
        { name: "Arduino", level: 95, color: "bg-teal-500" },
        { name: "Embedded Systems", level: 90, color: "bg-gray-500" },
        { name: "Sensor Integration", level: 92, color: "bg-yellow-500" },
        { name: "Motor Control", level: 88, color: "bg-red-500" },
        { name: "Wireless Communication", level: 85, color: "bg-blue-500" }
      ]
    },
    {
      title: "Databases & Tools",
      icon: Database,
      color: "from-orange-400 to-red-400",
      skills: [
        { name: "MySQL", level: 85, color: "bg-blue-500" },
        { name: "Statistical Analysis", level: 88, color: "bg-green-500" },
        { name: "Linux", level: 82, color: "bg-yellow-500" },
        { name: "Git", level: 90, color: "bg-orange-500" },
        { name: "CUDA", level: 75, color: "bg-green-600" }
      ]
    }
  ]

  const languages = [
    { name: "Bangla", level: 100, flag: "🇧🇩" },
    { name: "English", level: 95, flag: "🇺🇸" },
    { name: "Hindi", level: 70, flag: "🇮🇳" }
  ]

  const certifications = [
    {
      title: "Supervised Machine Learning",
      provider: "DeepLearning.AI",
      icon: Brain,
      color: "from-blue-500 to-purple-500"
    },
    {
      title: "Programming for Everybody",
      provider: "University of Michigan",
      icon: Code,
      color: "from-green-500 to-blue-500"
    },
    {
      title: "Python 3 for Robotics",
      provider: "Robotics Academy",
      icon: Cpu,
      color: "from-yellow-500 to-orange-500"
    },
    {
      title: "Linux for Robotics",
      provider: "Tech Institute",
      icon: Wrench,
      color: "from-purple-500 to-pink-500"
    }
  ]

  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Technical <span className="gradient-text">Skills</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            A comprehensive overview of my technical expertise across various domains 
            of computer science, AI/ML, and robotics.
          </p>
        </motion.div>

        {/* Skills Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {skillCategories.map((category, categoryIndex) => {
            const Icon = category.icon
            return (
              <motion.div
                key={category.title}
                className="glass-effect rounded-xl p-8 border-l-4 border-gradient-to-b"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
                viewport={{ once: true }}
                style={{ borderImage: `linear-gradient(to bottom, ${category.color.split(' ')[1]}, ${category.color.split(' ')[3]}) 1` }}
              >
                <div className="flex items-center mb-6">
                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center mr-4`}>
                    <Icon className="text-white" size={24} />
                  </div>
                  <h3 className="text-2xl font-bold gradient-text">{category.title}</h3>
                </div>

                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <motion.div
                      key={skill.name}
                      className="space-y-2"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}
                      viewport={{ once: true }}
                    >
                      <div className="flex justify-between items-center">
                        <span className="text-white font-medium">{skill.name}</span>
                        <span className="text-gray-400 text-sm">{skill.level}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
                        <motion.div
                          className={`h-full ${skill.color} rounded-full`}
                          initial={{ width: 0 }}
                          whileInView={{ width: `${skill.level}%` }}
                          transition={{ duration: 1, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}
                          viewport={{ once: true }}
                        />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Languages & Certifications */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Languages */}
          <motion.div
            className="glass-effect rounded-xl p-8 border-l-4 border-cyan-500"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-cyan-400 to-blue-400 flex items-center justify-center mr-4">
                <Globe className="text-white" size={24} />
              </div>
              <h3 className="text-2xl font-bold gradient-text">Languages</h3>
            </div>

            <div className="space-y-4">
              {languages.map((language, index) => (
                <motion.div
                  key={language.name}
                  className="flex items-center justify-between p-4 rounded-lg bg-gradient-to-r from-gray-800/50 to-gray-700/30 border border-gray-600/30"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{language.flag}</span>
                    <span className="text-white font-medium">{language.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-700 rounded-full h-2">
                      <motion.div
                        className="h-full bg-gradient-to-r from-green-400 to-blue-400 rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: `${language.level}%` }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                        viewport={{ once: true }}
                      />
                    </div>
                    <span className="text-gray-400 text-sm w-8">{language.level}%</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Certifications */}
          <motion.div
            className="glass-effect rounded-xl p-8 border-l-4 border-purple-500"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center mr-4">
                <BookOpen className="text-white" size={24} />
              </div>
              <h3 className="text-2xl font-bold gradient-text">Certifications</h3>
            </div>

            <div className="space-y-4">
              {certifications.map((cert, index) => {
                const Icon = cert.icon
                return (
                  <motion.div
                    key={cert.title}
                    className="p-4 rounded-lg bg-gradient-to-r from-gray-800/50 to-gray-700/30 border border-gray-600/30 hover:border-gray-500/50 transition-all duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${cert.color}`}>
                        <Icon className="text-white" size={16} />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-white text-sm mb-1">{cert.title}</h4>
                        <p className="text-gray-300 text-xs">{cert.provider}</p>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default Skills
