import React from 'react'
import { motion } from 'framer-motion'
import { Code, Database, Brain, Cpu, Zap, Globe, Wrench, BookOpen, TrendingUp, Activity, BarChart3 } from 'lucide-react'
import { <PERSON><PERSON>Bar<PERSON>hart, RadialBar, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, LineChart, Line, Area, AreaChart } from 'recharts'

const Skills: React.FC = () => {
  // Chart data for visualizations
  const skillsRadialData = [
    { name: 'Python', value: 95, fill: '#3B82F6' },
    { name: 'AI/ML', value: 92, fill: '#10B981' },
    { name: 'Robotics', value: 90, fill: '#F59E0B' },
    { name: 'C++', value: 88, fill: '#EF4444' },
    { name: 'JavaScript', value: 85, fill: '#8B5CF6' },
  ]

  const projectsData = [
    { month: 'Jan', projects: 2, learning: 8 },
    { month: 'Feb', projects: 3, learning: 9 },
    { month: 'Mar', projects: 4, learning: 7 },
    { month: 'Apr', projects: 5, learning: 8 },
    { month: 'May', projects: 3, learning: 9 },
    { month: 'Jun', projects: 6, learning: 10 },
  ]

  const activityData = [
    { name: 'Coding', hours: 8, color: '#3B82F6' },
    { name: 'Research', hours: 4, color: '#10B981' },
    { name: 'Learning', hours: 6, color: '#F59E0B' },
    { name: 'Projects', hours: 4, color: '#EF4444' },
    { name: 'Teaching', hours: 2, color: '#8B5CF6' },
  ]

  const growthData = [
    { year: '2021', skills: 60, projects: 2 },
    { year: '2022', skills: 75, projects: 5 },
    { year: '2023', skills: 88, projects: 8 },
    { year: '2024', skills: 95, projects: 12 },
  ]

  const skillCategories = [
    {
      title: "Programming Languages",
      icon: Code,
      color: "from-blue-400 to-cyan-400",
      skills: [
        { name: "Python", level: 95, color: "bg-yellow-500" },
        { name: "C++", level: 90, color: "bg-blue-500" },
        { name: "JavaScript", level: 85, color: "bg-yellow-400" },
        { name: "PHP", level: 80, color: "bg-purple-500" },
        { name: "HTML/CSS", level: 90, color: "bg-orange-500" }
      ]
    },
    {
      title: "AI/ML & Data Science",
      icon: Brain,
      color: "from-purple-400 to-pink-400",
      skills: [
        { name: "Machine Learning", level: 92, color: "bg-green-500" },
        { name: "TensorFlow", level: 88, color: "bg-orange-500" },
        { name: "PyTorch", level: 85, color: "bg-red-500" },
        { name: "Computer Vision", level: 87, color: "bg-blue-500" },
        { name: "NLP", level: 83, color: "bg-purple-500" },
        { name: "Reinforcement Learning", level: 80, color: "bg-indigo-500" }
      ]
    },
    {
      title: "Robotics & Hardware",
      icon: Cpu,
      color: "from-green-400 to-emerald-400",
      skills: [
        { name: "Arduino", level: 95, color: "bg-teal-500" },
        { name: "Embedded Systems", level: 90, color: "bg-gray-500" },
        { name: "Sensor Integration", level: 92, color: "bg-yellow-500" },
        { name: "Motor Control", level: 88, color: "bg-red-500" },
        { name: "Wireless Communication", level: 85, color: "bg-blue-500" }
      ]
    },
    {
      title: "Databases & Tools",
      icon: Database,
      color: "from-orange-400 to-red-400",
      skills: [
        { name: "MySQL", level: 85, color: "bg-blue-500" },
        { name: "Statistical Analysis", level: 88, color: "bg-green-500" },
        { name: "Linux", level: 82, color: "bg-yellow-500" },
        { name: "Git", level: 90, color: "bg-orange-500" },
        { name: "CUDA", level: 75, color: "bg-green-600" }
      ]
    }
  ]

  const languages = [
    { name: "Bangla", level: 100, flag: "🇧🇩" },
    { name: "English", level: 95, flag: "🇺🇸" },
    { name: "Hindi", level: 70, flag: "🇮🇳" }
  ]

  const certifications = [
    {
      title: "Supervised Machine Learning",
      provider: "DeepLearning.AI",
      icon: Brain,
      color: "from-blue-500 to-purple-500"
    },
    {
      title: "Programming for Everybody",
      provider: "University of Michigan",
      icon: Code,
      color: "from-green-500 to-blue-500"
    },
    {
      title: "Python 3 for Robotics",
      provider: "Robotics Academy",
      icon: Cpu,
      color: "from-yellow-500 to-orange-500"
    },
    {
      title: "Linux for Robotics",
      provider: "Tech Institute",
      icon: Wrench,
      color: "from-purple-500 to-pink-500"
    }
  ]

  return (
    <div className="section-padding py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
      <div className="container-max max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12 lg:mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black tech-font mb-4 lg:mb-6">
            Technical <span className="gradient-text">Skills</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            A comprehensive overview of my technical expertise across various domains
            of computer science, AI/ML, and robotics.
          </p>
        </motion.div>

        {/* Charts and Visualizations */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8 mb-12 lg:mb-16">
          {/* Skills Radial Chart */}
          <motion.div
            className="lg:col-span-1 glass-effect rounded-2xl p-8 border-l-4 border-blue-500"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-400 to-cyan-400 flex items-center justify-center mr-4">
                <TrendingUp className="text-white" size={24} />
              </div>
              <h3 className="text-2xl font-bold gradient-text tech-font">Top Skills</h3>
            </div>
            <div className="h-64 sm:h-80">
              <ResponsiveContainer width="100%" height="100%">
                <RadialBarChart cx="50%" cy="50%" innerRadius="20%" outerRadius="90%" data={skillsRadialData}>
                  <RadialBar
                    dataKey="value"
                    cornerRadius={10}
                    fill="#8884d8"
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                </RadialBarChart>
              </ResponsiveContainer>
            </div>
          </motion.div>

          {/* Daily Activity Pie Chart */}
          <motion.div
            className="lg:col-span-1 glass-effect rounded-2xl p-8 border-l-4 border-green-500"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-green-400 to-emerald-400 flex items-center justify-center mr-4">
                <Activity className="text-white" size={24} />
              </div>
              <h3 className="text-2xl font-bold gradient-text tech-font">Daily Activity</h3>
            </div>
            <div className="h-64 sm:h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={activityData}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="hours"
                    label={({ name, hours }) => `${name}: ${hours}h`}
                  >
                    {activityData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </motion.div>

          {/* Growth Over Time */}
          <motion.div
            className="lg:col-span-2 xl:col-span-1 glass-effect rounded-2xl p-8 border-l-4 border-purple-500"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center mr-4">
                <TrendingUp className="text-white" size={24} />
              </div>
              <h3 className="text-2xl font-bold gradient-text tech-font">Growth</h3>
            </div>
            <div className="h-64 sm:h-80">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={growthData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="year" stroke="#9CA3AF" />
                  <YAxis stroke="#9CA3AF" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                  <Area
                    type="monotone"
                    dataKey="skills"
                    stroke="#8B5CF6"
                    fill="url(#skillsGradient)"
                    strokeWidth={3}
                  />
                  <Area
                    type="monotone"
                    dataKey="projects"
                    stroke="#10B981"
                    fill="url(#projectsGradient)"
                    strokeWidth={3}
                  />
                  <defs>
                    <linearGradient id="skillsGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="projectsGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10B981" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#10B981" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </motion.div>
        </div>

        {/* Monthly Projects Activity */}
        <motion.div
          className="glass-effect rounded-2xl p-8 border-l-4 border-yellow-500 mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center mb-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-yellow-400 to-orange-400 flex items-center justify-center mr-4">
              <BarChart className="text-white" size={24} />
            </div>
            <h3 className="text-3xl font-bold gradient-text tech-font">Monthly Activity</h3>
          </div>
          <div className="h-72 sm:h-96">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={projectsData}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis dataKey="month" stroke="#9CA3AF" />
                <YAxis stroke="#9CA3AF" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
                <Bar dataKey="projects" fill="#3B82F6" radius={[4, 4, 0, 0]} />
                <Bar dataKey="learning" fill="#10B981" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Skills Categories with Progress Bars */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {skillCategories.map((category, categoryIndex) => {
            const Icon = category.icon
            return (
              <motion.div
                key={category.title}
                className="glass-effect rounded-2xl p-8 border-l-4 border-gradient-to-b"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
                viewport={{ once: true }}
                style={{ borderImage: `linear-gradient(to bottom, ${category.color.split(' ')[1]}, ${category.color.split(' ')[3]}) 1` }}
              >
                <div className="flex items-center mb-6">
                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center mr-4`}>
                    <Icon className="text-white" size={24} />
                  </div>
                  <h3 className="text-2xl font-bold gradient-text tech-font">{category.title}</h3>
                </div>

                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <motion.div
                      key={skill.name}
                      className="space-y-2"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}
                      viewport={{ once: true }}
                    >
                      <div className="flex justify-between items-center">
                        <span className="text-white font-medium">{skill.name}</span>
                        <span className="text-gray-400 text-sm font-bold">{skill.level}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-3 overflow-hidden">
                        <motion.div
                          className={`h-full ${skill.color} rounded-full relative`}
                          initial={{ width: 0 }}
                          whileInView={{ width: `${skill.level}%` }}
                          transition={{ duration: 1.5, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}
                          viewport={{ once: true }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                        </motion.div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Languages & Certifications */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Languages */}
          <motion.div
            className="glass-effect rounded-xl p-8 border-l-4 border-cyan-500"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-cyan-400 to-blue-400 flex items-center justify-center mr-4">
                <Globe className="text-white" size={24} />
              </div>
              <h3 className="text-2xl font-bold gradient-text">Languages</h3>
            </div>

            <div className="space-y-4">
              {languages.map((language, index) => (
                <motion.div
                  key={language.name}
                  className="flex items-center justify-between p-4 rounded-lg bg-gradient-to-r from-gray-800/50 to-gray-700/30 border border-gray-600/30"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{language.flag}</span>
                    <span className="text-white font-medium">{language.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-700 rounded-full h-2">
                      <motion.div
                        className="h-full bg-gradient-to-r from-green-400 to-blue-400 rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: `${language.level}%` }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                        viewport={{ once: true }}
                      />
                    </div>
                    <span className="text-gray-400 text-sm w-8">{language.level}%</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Certifications */}
          <motion.div
            className="glass-effect rounded-xl p-8 border-l-4 border-purple-500"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center mr-4">
                <BookOpen className="text-white" size={24} />
              </div>
              <h3 className="text-2xl font-bold gradient-text">Certifications</h3>
            </div>

            <div className="space-y-4">
              {certifications.map((cert, index) => {
                const Icon = cert.icon
                return (
                  <motion.div
                    key={cert.title}
                    className="p-4 rounded-lg bg-gradient-to-r from-gray-800/50 to-gray-700/30 border border-gray-600/30 hover:border-gray-500/50 transition-all duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${cert.color}`}>
                        <Icon className="text-white" size={16} />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-white text-sm mb-1">{cert.title}</h4>
                        <p className="text-gray-300 text-xs">{cert.provider}</p>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default Skills
