import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'

const LoadingScreen: React.FC = () => {
  const [progress, setProgress] = useState(0)
  const [loadingText, setLoadingText] = useState('Initializing AI Systems')

  const loadingSteps = [
    'Initializing AI Systems',
    'Loading 3D Models',
    'Preparing ML Algorithms',
    'Connecting Neural Networks',
    'Calibrating Robotics Data',
    'Finalizing Portfolio'
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 2
        const stepIndex = Math.floor((newProgress / 100) * loadingSteps.length)
        if (stepIndex < loadingSteps.length) {
          setLoadingText(loadingSteps[stepIndex])
        }
        return newProgress > 100 ? 100 : newProgress
      })
    }, 50)

    return () => clearInterval(interval)
  }, [])

  return (
    <motion.div
      className="fixed inset-0 bg-black flex flex-col items-center justify-center z-50"
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-green-900/20" />
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 2 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center">
        {/* Logo/Name */}
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="mb-8"
        >
          <h1 className="text-6xl font-bold gradient-text mb-2">
            POLOK PODDAR
          </h1>
          <p className="text-xl text-gray-300">
            AI/ML Engineer & Robotics Innovator
          </p>
        </motion.div>

        {/* Loading Animation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="mb-8"
        >
          {/* Circuit-like Loading Animation */}
          <div className="relative w-32 h-32 mx-auto mb-6">
            <svg className="w-full h-full" viewBox="0 0 100 100">
              <motion.circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="rgba(0, 255, 136, 0.3)"
                strokeWidth="2"
                strokeDasharray="283"
                strokeDashoffset="283"
                animate={{
                  strokeDashoffset: 283 - (283 * progress) / 100,
                }}
                transition={{ duration: 0.1 }}
              />
              <motion.circle
                cx="50"
                cy="50"
                r="35"
                fill="none"
                stroke="rgba(0, 212, 255, 0.5)"
                strokeWidth="1"
                strokeDasharray="220"
                strokeDashoffset="220"
                animate={{
                  strokeDashoffset: 220 - (220 * progress) / 100,
                  rotate: progress * 3.6,
                }}
                transition={{ duration: 0.1 }}
                style={{ transformOrigin: '50px 50px' }}
              />
              <motion.circle
                cx="50"
                cy="50"
                r="25"
                fill="none"
                stroke="rgba(255, 255, 255, 0.7)"
                strokeWidth="1"
                strokeDasharray="157"
                strokeDashoffset="157"
                animate={{
                  strokeDashoffset: 157 - (157 * progress) / 100,
                  rotate: -progress * 2.4,
                }}
                transition={{ duration: 0.1 }}
                style={{ transformOrigin: '50px 50px' }}
              />
            </svg>
            
            {/* Center Progress */}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-2xl font-bold text-white">
                {Math.round(progress)}%
              </span>
            </div>
          </div>

          {/* Loading Text */}
          <motion.p
            key={loadingText}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-lg text-gray-300 mb-4"
          >
            {loadingText}
            <span className="loading-dots">...</span>
          </motion.p>

          {/* Progress Bar */}
          <div className="w-64 h-1 bg-gray-800 rounded-full mx-auto overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-green-400 to-blue-400 rounded-full"
              style={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>
        </motion.div>

        {/* Tagline */}
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="text-sm text-gray-500 italic"
        >
          "Imagination is more important than innovation"
        </motion.p>
      </div>
    </motion.div>
  )
}

export default LoadingScreen
