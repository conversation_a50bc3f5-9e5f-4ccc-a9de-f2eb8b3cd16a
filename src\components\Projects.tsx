import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ExternalLink, Github, Award, Cpu, Brain, Car, Leaf, Gamepad2, Heart, Map, Code } from 'lucide-react'

interface Project {
  id: string
  title: string
  category: string
  description: string
  longDescription: string
  technologies: string[]
  achievements?: string[]
  icon: React.ComponentType<any>
  color: string
  image?: string
}

const Projects: React.FC = () => {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [filter, setFilter] = useState('all')

  const projects: Project[] = [
    {
      id: 'ovijan-v2',
      title: 'OVIJAN V2 - National Champion Rescue Robot',
      category: 'robotics',
      description: 'Advanced rescue robot with AI-driven decision making and GPS integration',
      longDescription: 'Designed an advanced rescue robot capable of navigating hazardous environments and detecting obstacles using multiple sensors. Won the National Championship and secured 6th place globally at the TechnoxianWorld Robotics Championship. Integrated GPS and AI-driven decision-making for real-time data collection and mapping.',
      technologies: ['C++', 'Arduino', 'GPS', 'AI', 'Sensors', 'Wireless Communication'],
      achievements: ['🏆 National Champion', '🌍 6th Place Globally', '🤖 AI Integration'],
      icon: Award,
      color: 'from-yellow-400 to-orange-500'
    },
    {
      id: 'autonomous-vehicle',
      title: 'Autonomous Vehicle with GenAI and RL',
      category: 'ai-ml',
      description: 'Self-learning autonomous vehicle using Reinforcement Learning and Generative AI',
      longDescription: 'Developed a self-learning autonomous vehicle using Reinforcement Learning (RL) and Generative AI (GenAI) for real-time navigation. Implemented computer vision-based lane detection and obstacle avoidance for efficient path-following. Trained the model in a simulation environment and tested its learning behavior.',
      technologies: ['Python', 'TensorFlow', 'OpenCV', 'Reinforcement Learning', 'Computer Vision', 'GenAI'],
      achievements: ['🧠 Self-Learning AI', '👁️ Computer Vision', '🛣️ Lane Detection'],
      icon: Car,
      color: 'from-blue-400 to-purple-500'
    },
    {
      id: 'agricultural-ai',
      title: 'AI-Driven Agricultural Tool',
      category: 'ai-ml',
      description: 'Crop health monitoring and weather forecasting system',
      longDescription: 'Developed an AI-powered system for real-time crop health monitoring and weather forecasting. Implemented LSTM for weather prediction and ML-based pest identification. Used Reinforcement Learning to improve prediction accuracy over time. Integrated geo-tagging for tracking abnormal crop conditions and optimizing agricultural decisions.',
      technologies: ['Python', 'LSTM', 'Machine Learning', 'Geo-tagging', 'IoT', 'Data Analytics'],
      achievements: ['🌱 Crop Health Monitoring', '🌤️ Weather Prediction', '📍 Geo-tagging'],
      icon: Leaf,
      color: 'from-green-400 to-emerald-500'
    },
    {
      id: 'boxing-robot',
      title: 'Boxing Robot - Inspired by Real Steel',
      category: 'robotics',
      description: 'Servo-powered boxing robot with motion control system',
      longDescription: 'Designed a servo-powered boxing robot capable of mimicking human punches and defensive moves. Developed a motion control system for fluid and responsive robot movements. Implemented real-time video synchronization for performance analysis and training.',
      technologies: ['Arduino', 'Servo Motors', 'Motion Control', 'Video Processing', 'Real-time Systems'],
      achievements: ['🥊 Human-like Movements', '📹 Video Sync', '⚡ Real-time Control'],
      icon: Gamepad2,
      color: 'from-red-400 to-pink-500'
    },
    {
      id: 'mongol-barta',
      title: 'Mongol Barta - Health Management System',
      category: 'software',
      description: 'Comprehensive health management system with predictive analytics',
      longDescription: 'Created a health management system to track and analyze patient health records efficiently. Designed a user-friendly interface for doctors and patients, enabling easy access to medical history. Implemented data analytics for predicting health trends based on recorded symptoms.',
      technologies: ['PHP', 'MySQL', 'Data Analytics', 'Web Development', 'UI/UX'],
      achievements: ['🏥 Patient Management', '📊 Health Analytics', '👨‍⚕️ Doctor Interface'],
      icon: Heart,
      color: 'from-pink-400 to-rose-500'
    },
    {
      id: 'bangla-voice',
      title: 'BanglaVoice Assistant',
      category: 'ai-ml',
      description: 'Bengali voice-controlled AI assistant with NLP',
      longDescription: 'Developed a Bengali-language voice assistant for real-time speech recognition and automation. Integrated AI-powered NLP to understand and execute voice commands in Bengali. Designed to control IoT devices and provide interactive responses to user queries.',
      technologies: ['Python', 'NLP', 'Speech Recognition', 'IoT', 'Bengali Processing'],
      achievements: ['🗣️ Bengali Speech Recognition', '🏠 IoT Control', '🤖 AI-powered NLP'],
      icon: Brain,
      color: 'from-indigo-400 to-blue-500'
    },
    {
      id: '2d-mapping',
      title: '2D Mapping using Sonar Sensors',
      category: 'research',
      description: 'Real-time spatial mapping system using ultrasonic sensors',
      longDescription: 'Developing a 2D mapping system using three HC-SR04 ultrasonic sensors. Aims to generate an accurate spatial representation of the surroundings in real-time.',
      technologies: ['C++', 'Arduino', 'Ultrasonic Sensors', 'Mapping Algorithms', 'Real-time Processing'],
      achievements: ['🗺️ Real-time Mapping', '📡 Sensor Fusion', '🎯 Spatial Accuracy'],
      icon: Map,
      color: 'from-cyan-400 to-teal-500'
    },
    {
      id: 'rccontrol-library',
      title: 'RCControl Library for Arduino',
      category: 'software',
      description: 'Simplified RC car and motor control library',
      longDescription: 'Created a new Arduino library to streamline the control of RC cars and DC motors. Simplifies motor control logic, making it easier for robotics and automation projects. Enables users to focus on building without worrying about low-level motor control details.',
      technologies: ['C++', 'Arduino', 'Library Development', 'Motor Control', 'Automation'],
      achievements: ['📚 Open Source Library', '🚗 RC Car Control', '⚙️ Motor Simplification'],
      icon: Code,
      color: 'from-purple-400 to-violet-500'
    }
  ]

  const categories = [
    { id: 'all', label: 'All Projects', count: projects.length },
    { id: 'robotics', label: 'Robotics', count: projects.filter(p => p.category === 'robotics').length },
    { id: 'ai-ml', label: 'AI/ML', count: projects.filter(p => p.category === 'ai-ml').length },
    { id: 'software', label: 'Software', count: projects.filter(p => p.category === 'software').length },
    { id: 'research', label: 'Research', count: projects.filter(p => p.category === 'research').length }
  ]

  const filteredProjects = filter === 'all' ? projects : projects.filter(p => p.category === filter)

  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            My <span className="gradient-text">Projects</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Explore my journey through innovative robotics, cutting-edge AI/ML solutions, 
            and impactful software development projects.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {categories.map((category) => (
            <motion.button
              key={category.id}
              onClick={() => setFilter(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                filter === category.id
                  ? 'bg-gradient-to-r from-green-400 to-blue-400 text-black'
                  : 'glass-effect text-gray-300 hover:text-white'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category.label} ({category.count})
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          layout
        >
          <AnimatePresence>
            {filteredProjects.map((project, index) => {
              const Icon = project.icon
              return (
                <motion.div
                  key={project.id}
                  className="glass-effect rounded-xl p-6 cursor-pointer hover:scale-105 transition-all duration-300"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 50 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -10 }}
                  onClick={() => setSelectedProject(project)}
                  layout
                >
                  {/* Project Icon */}
                  <div className={`w-16 h-16 rounded-lg bg-gradient-to-r ${project.color} flex items-center justify-center mb-4`}>
                    <Icon className="text-white" size={24} />
                  </div>

                  {/* Project Info */}
                  <h3 className="text-xl font-bold text-white mb-2">{project.title}</h3>
                  <p className="text-gray-300 text-sm mb-4 line-clamp-3">{project.description}</p>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.slice(0, 3).map((tech) => (
                      <span
                        key={tech}
                        className="px-2 py-1 bg-white/10 rounded text-xs text-gray-300"
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span className="px-2 py-1 bg-white/10 rounded text-xs text-gray-300">
                        +{project.technologies.length - 3} more
                      </span>
                    )}
                  </div>

                  {/* Achievements */}
                  {project.achievements && (
                    <div className="space-y-1">
                      {project.achievements.slice(0, 2).map((achievement, i) => (
                        <div key={i} className="text-xs text-green-400">
                          {achievement}
                        </div>
                      ))}
                    </div>
                  )}
                </motion.div>
              )
            })}
          </AnimatePresence>
        </motion.div>

        {/* Project Modal */}
        <AnimatePresence>
          {selectedProject && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {/* Backdrop */}
              <motion.div
                className="absolute inset-0 bg-black/80 backdrop-blur-sm"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setSelectedProject(null)}
              />

              {/* Modal Content */}
              <motion.div
                className="relative glass-effect rounded-xl p-8 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 50 }}
                transition={{ duration: 0.3 }}
              >
                <button
                  onClick={() => setSelectedProject(null)}
                  className="absolute top-4 right-4 text-gray-400 hover:text-white"
                >
                  ✕
                </button>

                <div className={`w-20 h-20 rounded-lg bg-gradient-to-r ${selectedProject.color} flex items-center justify-center mb-6`}>
                  <selectedProject.icon className="text-white" size={32} />
                </div>

                <h3 className="text-2xl font-bold text-white mb-4">{selectedProject.title}</h3>
                <p className="text-gray-300 mb-6">{selectedProject.longDescription}</p>

                {/* Technologies */}
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-white mb-3">Technologies Used</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedProject.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-3 py-1 bg-white/10 rounded-full text-sm text-gray-300"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Achievements */}
                {selectedProject.achievements && (
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-white mb-3">Key Achievements</h4>
                    <div className="space-y-2">
                      {selectedProject.achievements.map((achievement, i) => (
                        <div key={i} className="text-green-400">
                          {achievement}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default Projects
