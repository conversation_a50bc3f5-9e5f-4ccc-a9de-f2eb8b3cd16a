{"name": "port-pfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^6.3.5"}, "main": "index.js", "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@tensorflow/tfjs": "^4.22.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "chart.js": "^4.4.9", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "postcss": "^8.5.3", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "recharts": "^2.15.3", "tailwindcss": "^4.1.7", "three": "^0.176.0"}}