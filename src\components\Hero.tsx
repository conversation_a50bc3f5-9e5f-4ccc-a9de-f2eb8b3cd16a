import React, { useRef, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Points, PointMaterial, Text3D, OrbitControls } from '@react-three/drei'
import { motion } from 'framer-motion'
import { ChevronDown, Github, Linkedin, Mail, MapPin, Phone } from 'lucide-react'
import * as THREE from 'three'

// 3D Particle System
function ParticleField() {
  const ref = useRef<THREE.Points>(null!)

  const particlesPosition = useMemo(() => {
    const positions = new Float32Array(2000 * 3)
    for (let i = 0; i < 2000; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20
    }
    return positions
  }, [])

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.x = state.clock.elapsedTime * 0.05
      ref.current.rotation.y = state.clock.elapsedTime * 0.075
    }
  })

  return (
    <Points ref={ref} positions={particlesPosition} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color="#00ff88"
        size={0.05}
        sizeAttenuation={true}
        depthWrite={false}
      />
    </Points>
  )
}

// Floating 3D Robot Model (simplified)
function FloatingRobot() {
  const meshRef = useRef<THREE.Group>(null!)

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.3
    }
  })

  return (
    <group ref={meshRef} position={[3, 0, 0]}>
      {/* Robot Body */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[0.8, 1.2, 0.6]} />
        <meshStandardMaterial color="#333" metalness={0.8} roughness={0.2} />
      </mesh>

      {/* Robot Head */}
      <mesh position={[0, 0.8, 0]}>
        <boxGeometry args={[0.6, 0.6, 0.6]} />
        <meshStandardMaterial color="#555" metalness={0.8} roughness={0.2} />
      </mesh>

      {/* Eyes */}
      <mesh position={[-0.15, 0.9, 0.31]}>
        <sphereGeometry args={[0.08]} />
        <meshStandardMaterial color="#00ff88" emissive="#00ff88" emissiveIntensity={0.5} />
      </mesh>
      <mesh position={[0.15, 0.9, 0.31]}>
        <sphereGeometry args={[0.08]} />
        <meshStandardMaterial color="#00ff88" emissive="#00ff88" emissiveIntensity={0.5} />
      </mesh>

      {/* Arms */}
      <mesh position={[-0.6, 0.2, 0]}>
        <cylinderGeometry args={[0.1, 0.1, 0.8]} />
        <meshStandardMaterial color="#444" metalness={0.8} roughness={0.2} />
      </mesh>
      <mesh position={[0.6, 0.2, 0]}>
        <cylinderGeometry args={[0.1, 0.1, 0.8]} />
        <meshStandardMaterial color="#444" metalness={0.8} roughness={0.2} />
      </mesh>
    </group>
  )
}

const Hero: React.FC = () => {
  const scrollToNext = () => {
    const aboutSection = document.getElementById('about')
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* 3D Background */}
      <div className="absolute inset-0 z-0">
        <Canvas camera={{ position: [0, 0, 8], fov: 60 }}>
          <ambientLight intensity={0.3} />
          <pointLight position={[10, 10, 10]} intensity={1.2} />
          <pointLight position={[-10, -10, -10]} intensity={0.8} color="#00d4ff" />
          <spotLight position={[0, 20, 0]} intensity={0.5} color="#ff6b6b" />

          <ParticleField />
          <FloatingRobot />

          <OrbitControls
            enableZoom={false}
            enablePan={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={0.3}
          />
        </Canvas>
      </div>

      {/* Content */}
      <div className="relative z-10 w-full max-w-6xl mx-auto px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="space-y-8"
        >
          {/* Main Title */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1, delay: 0.4 }}
            className="space-y-4"
          >
            <h1 className="text-6xl md:text-8xl lg:text-9xl font-black tech-font tracking-tight">
              <span className="gradient-text">POLOK</span>
            </h1>
            <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white/90 tracking-wide">
              PODDAR
            </h2>
          </motion.div>

          {/* Professional Title */}
          <motion.div
            className="space-y-4"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <div className="flex flex-wrap justify-center gap-4 text-lg md:text-xl lg:text-2xl">
              <span className="px-4 py-2 glass-effect rounded-full text-cyan-400 font-semibold">
                AI/ML Engineer
              </span>
              <span className="px-4 py-2 glass-effect rounded-full text-green-400 font-semibold">
                Robotics Expert
              </span>
              <span className="px-4 py-2 glass-effect rounded-full text-yellow-400 font-semibold">
                Python Specialist
              </span>
            </div>
            <div className="text-2xl md:text-3xl font-bold">
              <span className="text-orange-400">🏆 National Champion</span>
            </div>
          </motion.div>

          {/* Philosophy */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <p className="text-xl md:text-2xl text-gray-300 italic font-light max-w-3xl mx-auto">
              "Imagination is more important than innovation"
            </p>
            <p className="text-lg text-gray-400 max-w-4xl mx-auto leading-relaxed">
              I'm Polok Poddar, a passionate AI/ML engineer and robotics innovator from Bangladesh.
              I specialize in Python, machine learning, and building intelligent robotic systems.
              My work spans from rescue robotics to agricultural AI solutions.
            </p>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
          >
            <div className="glass-effect rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-green-400">6th</div>
              <div className="text-sm text-gray-300">Global Rank</div>
            </div>
            <div className="glass-effect rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-blue-400">10+</div>
              <div className="text-sm text-gray-300">AI Projects</div>
            </div>
            <div className="glass-effect rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-purple-400">5+</div>
              <div className="text-sm text-gray-300">Languages</div>
            </div>
            <div className="glass-effect rounded-xl p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">3+</div>
              <div className="text-sm text-gray-300">Years Exp</div>
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.6 }}
          >
            <motion.button
              className="px-8 py-4 bg-gradient-to-r from-green-400 via-blue-400 to-purple-400 text-black font-bold rounded-xl text-lg hover:shadow-2xl hover:shadow-green-400/30 transition-all duration-300"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
            >
              🚀 Explore My Projects
            </motion.button>
            <motion.button
              className="px-8 py-4 glass-effect border-2 border-white/30 text-white font-bold rounded-xl text-lg hover:bg-white/10 hover:border-white/50 transition-all duration-300"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
            >
              💬 Let's Connect
            </motion.button>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 2 }}
      >
        <motion.button
          onClick={scrollToNext}
          className="flex flex-col items-center text-gray-400 hover:text-white transition-colors duration-300 group"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <span className="text-sm mb-2 group-hover:text-green-400 transition-colors duration-300">
            Discover More
          </span>
          <ChevronDown size={28} className="group-hover:text-green-400 transition-colors duration-300" />
        </motion.button>
      </motion.div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/30 pointer-events-none" />
    </div>
  )
}

export default Hero
