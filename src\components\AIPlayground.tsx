import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Brain, Camera, Mic, Cloud, Cpu, Zap, Play, Pause, RotateCcw } from 'lucide-react'

const AIPlayground: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string | null>(null)
  const [isRunning, setIsRunning] = useState(false)

  const demos = [
    {
      id: 'image-classification',
      title: 'Image Classification',
      description: 'Upload an image and see AI classify objects in real-time',
      icon: Camera,
      color: 'from-blue-400 to-cyan-400',
      category: 'Computer Vision',
      difficulty: 'Beginner'
    },
    {
      id: 'weather-prediction',
      title: 'Weather Prediction',
      description: 'LSTM-based weather forecasting inspired by my agricultural AI project',
      icon: Cloud,
      color: 'from-green-400 to-emerald-400',
      category: 'Time Series',
      difficulty: 'Advanced'
    },
    {
      id: 'voice-recognition',
      title: 'Voice Recognition',
      description: 'Bengali voice command recognition demo',
      icon: Mic,
      color: 'from-purple-400 to-pink-400',
      category: 'NLP',
      difficulty: 'Intermediate'
    },
    {
      id: 'neural-network',
      title: 'Neural Network Visualizer',
      description: 'Interactive visualization of neural network training',
      icon: Brain,
      color: 'from-orange-400 to-red-400',
      category: 'Deep Learning',
      difficulty: 'Advanced'
    },
    {
      id: 'robotics-sim',
      title: 'Robotics Simulation',
      description: '3D simulation of OVIJAN rescue robot navigation',
      icon: Cpu,
      color: 'from-indigo-400 to-blue-400',
      category: 'Robotics',
      difficulty: 'Expert'
    },
    {
      id: 'reinforcement-learning',
      title: 'Reinforcement Learning',
      description: 'Watch an AI agent learn to navigate obstacles',
      icon: Zap,
      color: 'from-yellow-400 to-orange-400',
      category: 'RL',
      difficulty: 'Advanced'
    }
  ]

  const categories = ['All', 'Computer Vision', 'NLP', 'Deep Learning', 'Robotics', 'Time Series', 'RL']
  const [selectedCategory, setSelectedCategory] = useState('All')

  const filteredDemos = selectedCategory === 'All' 
    ? demos 
    : demos.filter(demo => demo.category === selectedCategory)

  const runDemo = (demoId: string) => {
    setActiveDemo(demoId)
    setIsRunning(true)
    
    // Simulate demo running
    setTimeout(() => {
      setIsRunning(false)
    }, 3000)
  }

  const DemoContent = ({ demo }: { demo: typeof demos[0] }) => {
    switch (demo.id) {
      case 'image-classification':
        return (
          <div className="space-y-4">
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center">
              <Camera className="mx-auto mb-4 text-gray-400" size={48} />
              <p className="text-gray-300">Drop an image here or click to upload</p>
            </div>
            {isRunning && (
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                  <span className="text-green-400">Analyzing image...</span>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Cat</span>
                    <span className="text-green-400">94.2%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Animal</span>
                    <span className="text-blue-400">87.6%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Mammal</span>
                    <span className="text-purple-400">82.1%</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )
      
      case 'weather-prediction':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-800 rounded-lg p-4">
                <h4 className="text-sm font-semibold mb-2">Current Conditions</h4>
                <div className="text-2xl font-bold text-blue-400">28°C</div>
                <div className="text-sm text-gray-400">Partly Cloudy</div>
              </div>
              <div className="bg-gray-800 rounded-lg p-4">
                <h4 className="text-sm font-semibold mb-2">Humidity</h4>
                <div className="text-2xl font-bold text-green-400">65%</div>
                <div className="text-sm text-gray-400">Normal</div>
              </div>
            </div>
            {isRunning && (
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <div className="w-3 h-3 bg-blue-400 rounded-full mr-2 animate-pulse"></div>
                  <span className="text-blue-400">LSTM Model Predicting...</span>
                </div>
                <div className="text-sm text-gray-300">
                  Tomorrow: 26°C, 70% chance of rain
                </div>
              </div>
            )}
          </div>
        )
      
      case 'voice-recognition':
        return (
          <div className="space-y-4">
            <div className="text-center">
              <div className="w-24 h-24 mx-auto bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center mb-4">
                <Mic className="text-white" size={32} />
              </div>
              <p className="text-gray-300">Say something in Bengali...</p>
            </div>
            {isRunning && (
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <div className="w-3 h-3 bg-purple-400 rounded-full mr-2 animate-pulse"></div>
                  <span className="text-purple-400">Processing Bengali speech...</span>
                </div>
                <div className="text-sm text-gray-300">
                  Recognized: "আলো জ্বালাও" (Turn on the light)
                </div>
              </div>
            )}
          </div>
        )
      
      default:
        return (
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-gray-600 to-gray-800 rounded-full flex items-center justify-center mb-4">
              <demo.icon className="text-white" size={24} />
            </div>
            <p className="text-gray-300">Demo coming soon...</p>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            AI <span className="gradient-text">Playground</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Interactive demonstrations of AI/ML technologies I've worked with. 
            Try out these demos to see artificial intelligence in action!
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-3 mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {categories.map((category) => (
            <motion.button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                selectedCategory === category
                  ? 'bg-gradient-to-r from-green-400 to-blue-400 text-black'
                  : 'glass-effect text-gray-300 hover:text-white'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category}
            </motion.button>
          ))}
        </motion.div>

        {/* Demos Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <AnimatePresence>
            {filteredDemos.map((demo, index) => {
              const Icon = demo.icon
              return (
                <motion.div
                  key={demo.id}
                  className="glass-effect rounded-xl p-6 border border-gray-700/30 hover:border-gray-500/50 transition-all duration-300"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 50 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  layout
                >
                  {/* Demo Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${demo.color} flex items-center justify-center`}>
                      <Icon className="text-white" size={20} />
                    </div>
                    <div className="text-right">
                      <span className="text-xs px-2 py-1 bg-gray-700 rounded-full text-gray-300">
                        {demo.difficulty}
                      </span>
                    </div>
                  </div>

                  {/* Demo Info */}
                  <h3 className="text-lg font-bold text-white mb-2">{demo.title}</h3>
                  <p className="text-gray-300 text-sm mb-4 line-clamp-2">{demo.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs px-2 py-1 bg-blue-500/20 text-blue-300 rounded">
                      {demo.category}
                    </span>
                    <motion.button
                      onClick={() => runDemo(demo.id)}
                      disabled={isRunning && activeDemo === demo.id}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                        isRunning && activeDemo === demo.id
                          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                          : 'bg-gradient-to-r from-green-400 to-blue-400 text-black hover:shadow-lg'
                      }`}
                      whileHover={!(isRunning && activeDemo === demo.id) ? { scale: 1.05 } : {}}
                      whileTap={!(isRunning && activeDemo === demo.id) ? { scale: 0.95 } : {}}
                    >
                      {isRunning && activeDemo === demo.id ? (
                        <div className="flex items-center">
                          <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin mr-2"></div>
                          Running
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Play size={14} className="mr-1" />
                          Try Demo
                        </div>
                      )}
                    </motion.button>
                  </div>
                </motion.div>
              )
            })}
          </AnimatePresence>
        </div>

        {/* Active Demo Display */}
        <AnimatePresence>
          {activeDemo && (
            <motion.div
              className="glass-effect rounded-xl p-8 border border-gray-600/50"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 30 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold gradient-text">
                  {demos.find(d => d.id === activeDemo)?.title}
                </h3>
                <div className="flex space-x-2">
                  <motion.button
                    onClick={() => runDemo(activeDemo)}
                    className="p-2 bg-green-500 rounded-lg text-white hover:bg-green-600 transition-colors duration-200"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <RotateCcw size={16} />
                  </motion.button>
                  <motion.button
                    onClick={() => setActiveDemo(null)}
                    className="p-2 bg-red-500 rounded-lg text-white hover:bg-red-600 transition-colors duration-200"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    ✕
                  </motion.button>
                </div>
              </div>
              
              <DemoContent demo={demos.find(d => d.id === activeDemo)!} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default AIPlayground
