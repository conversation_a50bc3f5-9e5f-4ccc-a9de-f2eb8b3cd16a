<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON>ddar - AI/ML Engineer & Robotics Innovator</title>
    <meta name="description" content="Portfolio of Polok Poddar - AI/ML Engineer, Robotics Expert, and National Champion. Explore interactive 3D projects and cutting-edge technology demonstrations." />
    <meta name="keywords" content="AI, Machine Learning, Robotics, Portfolio, Polok Poddar, Bangladesh, BRAC University" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Polok Poddar - AI/ML Engineer & Robotics Innovator" />
    <meta property="og:description" content="Explore the innovative world of AI, ML, and Robotics through interactive 3D experiences" />
    <meta property="og:type" content="website" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', sans-serif;
        background: #0a0a0a;
        color: #ffffff;
        overflow-x: hidden;
      }

      #root {
        width: 100%;
        min-height: 100vh;
      }

      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #0a0a0a;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid #333;
        border-top: 3px solid #00ff88;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">
        <div class="loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
