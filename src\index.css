@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', sans-serif;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  overflow-x: hidden;
  line-height: 1.6;
  min-height: 100vh;
  text-align: left;
  letter-spacing: 0.025em;
}

#root {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Utility classes */
.gradient-text {
  background: linear-gradient(135deg, #00ff88, #00d4ff, #ff6b6b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.neon-glow {
  box-shadow: 0 0 30px rgba(0, 255, 136, 0.4), 0 0 60px rgba(0, 255, 136, 0.2);
}

.tech-font {
  font-family: 'Orbitron', monospace;
}

.code-font {
  font-family: 'JetBrains Mono', monospace;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Text alignment utilities */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

/* Typography improvements */
h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
  font-weight: 600;
  letter-spacing: -0.025em;
}

p {
  line-height: 1.7;
  margin-bottom: 1rem;
}

.text-balance {
  text-wrap: balance;
}

/* Responsive design for all devices */
@media (max-width: 640px) {
  body {
    font-size: 14px;
  }

  h1 { font-size: 2rem !important; }
  h2 { font-size: 1.75rem !important; }
  h3 { font-size: 1.25rem !important; }

  .text-center-mobile {
    text-align: center !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  h1 { font-size: 2.5rem !important; }
  h2 { font-size: 2rem !important; }
  h3 { font-size: 1.5rem !important; }
}

@media (min-width: 769px) and (max-width: 1024px) {
  h1 { font-size: 3rem !important; }
  h2 { font-size: 2.25rem !important; }
  h3 { font-size: 1.75rem !important; }
}

/* Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 600px) {
  .hero-section {
    min-height: 100vh;
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 3rem !important;
  }

  .hero-subtitle {
    font-size: 1.5rem !important;
  }

  .section-padding {
    padding: 3rem 0;
  }
}

/* Ultra-wide screens */
@media (min-width: 1920px) {
  .container-max {
    max-width: 1600px;
  }
}

/* Small landscape phones */
@media (orientation: landscape) and (max-height: 480px) {
  .hero-section {
    padding: 1rem 0;
  }

  .hero-title {
    font-size: 2.5rem !important;
    line-height: 1.1;
  }

  .hero-content {
    gap: 1rem;
  }
}

/* Loading animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    color: rgba(0, 0, 0, 0);
    text-shadow:
      .25em 0 0 rgba(0, 0, 0, 0),
      .5em 0 0 rgba(0, 0, 0, 0);
  }
  40% {
    color: white;
    text-shadow:
      .25em 0 0 rgba(0, 0, 0, 0),
      .5em 0 0 rgba(0, 0, 0, 0);
  }
  60% {
    text-shadow:
      .25em 0 0 white,
      .5em 0 0 rgba(0, 0, 0, 0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 white,
      .5em 0 0 white;
  }
}
