import React from 'react'
import { motion } from 'framer-motion'
import { Award, GraduationCap, Users, Heart, Brain, Code, Zap } from 'lucide-react'

const AboutNew: React.FC = () => {
  const achievements = [
    {
      icon: Award,
      title: "National Champion",
      description: "OVIJAN V2 - 1st place in Technoxian Bangladesh 2024",
      color: "text-yellow-400"
    },
    {
      icon: Award,
      title: "Global 6th Place",
      description: "TechnoxianWorld Robotics Championship",
      color: "text-blue-400"
    },
    {
      icon: GraduationCap,
      title: "AI Internship",
      description: "Selected for CodSoft AI Internship Program",
      color: "text-green-400"
    },
    {
      icon: Users,
      title: "Leadership Role",
      description: "Assistant Director - BRAC University Robotics Club",
      color: "text-purple-400"
    }
  ]

  return (
    <div className="py-20 px-6 bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 relative overflow-hidden">
      {/* AI Circuit Background */}
      <div className="absolute inset-0 opacity-5">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2300ffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3Cpath d='M30 0v60M0 30h60'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* AI Terminal Header */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="glass-effect rounded-2xl p-8 border border-cyan-500/30 max-w-4xl mx-auto">
            <div className="flex items-center mb-6">
              <div className="flex space-x-2 mr-4">
                <div className="w-3 h-3 bg-red-500 rounded-full" />
                <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                <div className="w-3 h-3 bg-green-500 rounded-full" />
              </div>
              <span className="text-gray-400 font-mono text-sm">polok@ai-terminal:~$</span>
            </div>
            
            <div className="font-mono text-green-400 space-y-2">
              <div className="flex items-center">
                <span className="text-cyan-400">{'>'}</span>
                <span className="ml-2">whoami</span>
              </div>
              <div className="text-white pl-4">
                <h2 className="text-4xl md:text-5xl font-black mb-4">
                  POLOK PODDAR
                  <span className="text-cyan-400 animate-pulse">_</span>
                </h2>
                <p className="text-xl text-gray-300 leading-relaxed">
                  AI/ML Engineer | Robotics Innovator | Python Specialist
                  <br />
                  Building intelligent systems that bridge the gap between imagination and reality
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* AI Data Panels */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {/* Neural Network Simulation */}
          <motion.div
            className="lg:col-span-2 glass-effect rounded-2xl p-8 border border-blue-500/30"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center mb-6">
              <div className="w-4 h-4 bg-blue-400 rounded-full mr-3 animate-pulse" />
              <h3 className="text-2xl font-bold text-blue-400 font-mono">NEURAL_NETWORK.py</h3>
            </div>
            
            <div className="font-mono text-sm space-y-4">
              <div className="text-gray-400"># My AI/ML Journey</div>
              <div className="space-y-2">
                <div className="text-green-400">
                  <span className="text-purple-400">class</span> PolokPoddar:
                </div>
                <div className="pl-4 space-y-1">
                  <div><span className="text-blue-400">education</span> = <span className="text-yellow-400">"Computer Science & Engineering @ BRAC University"</span></div>
                  <div><span className="text-blue-400">thesis</span> = <span className="text-yellow-400">"AI-powered Agricultural Solutions"</span></div>
                  <div><span className="text-blue-400">specialization</span> = [<span className="text-yellow-400">"Python"</span>, <span className="text-yellow-400">"Machine Learning"</span>, <span className="text-yellow-400">"Robotics"</span>]</div>
                  <div><span className="text-blue-400">achievements</span> = [<span className="text-yellow-400">"National Champion"</span>, <span className="text-yellow-400">"Global 6th Place"</span>]</div>
                </div>
                
                <div className="text-green-400 mt-4">
                  <span className="text-purple-400">def</span> build_future():
                </div>
                <div className="pl-4 space-y-1">
                  <div className="text-gray-300">projects = [</div>
                  <div className="pl-4 space-y-1">
                    <div><span className="text-yellow-400">"OVIJAN V2 - Rescue Robot"</span>,</div>
                    <div><span className="text-yellow-400">"Agricultural AI System"</span>,</div>
                    <div><span className="text-yellow-400">"Bengali Voice Assistant"</span>,</div>
                    <div><span className="text-yellow-400">"Computer Vision Solutions"</span></div>
                  </div>
                  <div className="text-gray-300">]</div>
                  <div className="text-cyan-400">return <span className="text-yellow-400">"Innovation through AI"</span></div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* System Status */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Status Panel */}
            <div className="glass-effect rounded-2xl p-6 border border-green-500/30">
              <div className="flex items-center mb-4">
                <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse" />
                <span className="text-green-400 font-mono text-sm">SYSTEM_ONLINE</span>
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 font-mono">Python Mastery</span>
                  <span className="text-cyan-400 font-bold">95%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 font-mono">AI/ML Skills</span>
                  <span className="text-green-400 font-bold">92%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 font-mono">Robotics</span>
                  <span className="text-purple-400 font-bold">90%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 font-mono">Innovation</span>
                  <span className="text-yellow-400 font-bold">100%</span>
                </div>
              </div>
            </div>

            {/* Leadership Panel */}
            <div className="glass-effect rounded-2xl p-6 border border-purple-500/30">
              <h4 className="text-purple-400 font-mono font-bold mb-4">LEADERSHIP.exe</h4>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3" />
                  <span className="text-gray-300">Founder & CEO - dotPY Academy</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-3" />
                  <span className="text-gray-300">Assistant Director - BRAC Robotics Club</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-3" />
                  <span className="text-gray-300">AI Internship - CodSoft</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Achievements Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {achievements.map((achievement, index) => {
            const Icon = achievement.icon
            return (
              <motion.div
                key={index}
                className="glass-effect rounded-2xl p-6 border border-gray-600/40 hover:border-gray-500/60 transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02, y: -2 }}
              >
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${
                    index === 0 ? 'from-yellow-500 to-orange-500' :
                    index === 1 ? 'from-blue-500 to-cyan-500' :
                    index === 2 ? 'from-green-500 to-emerald-500' :
                    'from-purple-500 to-pink-500'
                  }`}>
                    <Icon className="text-white" size={24} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-bold text-white text-lg mb-1">{achievement.title}</h4>
                    <p className="text-gray-300 text-sm">{achievement.description}</p>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Philosophy & Contact */}
        <motion.div
          className="text-center glass-effect rounded-2xl p-12 border-l-4 border-cyan-400"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="max-w-4xl mx-auto space-y-8">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 rounded-xl bg-gradient-to-r from-cyan-400 to-blue-400 flex items-center justify-center mr-4">
                <Heart className="text-white" size={32} />
              </div>
              <h3 className="text-4xl font-bold gradient-text tech-font">My Philosophy</h3>
            </div>
            
            <blockquote className="text-3xl md:text-4xl font-light italic text-gray-300 leading-relaxed">
              "Imagination is more important than innovation"
            </blockquote>
            
            <p className="text-xl text-gray-400 leading-relaxed max-w-3xl mx-auto">
              I believe in pushing the boundaries of what's possible through creative thinking and 
              innovative solutions. Every robot I build, every AI model I train, and every line of 
              Python code I write is driven by the desire to make the world a better place.
            </p>

            {/* Quick Contact */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <div className="p-6 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30">
                <div className="text-2xl mb-2">📧</div>
                <div className="text-sm text-gray-300">Email</div>
                <div className="text-green-400 font-semibold"><EMAIL></div>
              </div>
              <div className="p-6 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30">
                <div className="text-2xl mb-2">📱</div>
                <div className="text-sm text-gray-300">Phone</div>
                <div className="text-blue-400 font-semibold">+8801770065234</div>
              </div>
              <div className="p-6 rounded-xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30">
                <div className="text-2xl mb-2">📍</div>
                <div className="text-sm text-gray-300">Location</div>
                <div className="text-purple-400 font-semibold">Shariatpur, Bangladesh</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default AboutNew
