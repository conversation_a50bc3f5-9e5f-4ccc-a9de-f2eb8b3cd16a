import React from 'react'
import { motion } from 'framer-motion'
import { GraduationCap, Award, Users, Clock, Heart, Zap } from 'lucide-react'

const About: React.FC = () => {
  const achievements = [
    {
      icon: Award,
      title: "National Champion",
      description: "OVIJAN V2 - 1st place in Technoxian Bangladesh National Round 2024",
      color: "text-yellow-400"
    },
    {
      icon: Award,
      title: "Global Recognition",
      description: "6th place globally at TechnoxianWorld Robotics Championship",
      color: "text-blue-400"
    },
    {
      icon: GraduationCap,
      title: "AI Internship",
      description: "Selected for 4-week AI internship at CodSoft",
      color: "text-green-400"
    },
    {
      icon: Users,
      title: "Leadership",
      description: "Assistant Director at ROBOTICS CLUB OF BRAC UNIVERSITY",
      color: "text-purple-400"
    }
  ]

  const dailyLife = [
    { activity: "Sleep", hours: 5, color: "bg-blue-500" },
    { activity: "Class", hours: 4, color: "bg-green-500" },
    { activity: "Productivity & Research", hours: 8, color: "bg-purple-500" },
    { activity: "Sports & Relaxation", hours: 3, color: "bg-yellow-500" },
    { activity: "Family Time", hours: 4, color: "bg-pink-500" }
  ]

  const strengths = [
    { name: "Hard-working", icon: Zap },
    { name: "Critical Thinking", icon: Heart },
    { name: "Motivator & Leader", icon: Users },
    { name: "Research & Productivity", icon: GraduationCap }
  ]

  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            About <span className="gradient-text">Me</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            A passionate AI/ML engineer and robotics innovator from Bangladesh,
            dedicated to pushing the boundaries of technology and innovation.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-16">
          {/* Personal Story - Takes 2 columns */}
          <motion.div
            className="xl:col-span-2 space-y-6"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Journey Card */}
            <div className="glass-effect rounded-xl p-8 border-l-4 border-gradient-to-b from-green-400 to-blue-400">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-green-400 to-blue-400 flex items-center justify-center mr-4">
                  <GraduationCap className="text-white" size={24} />
                </div>
                <h3 className="text-2xl font-bold gradient-text">My Journey</h3>
              </div>
              <div className="space-y-4 text-gray-300 leading-relaxed">
                <p className="text-lg">
                  Currently pursuing <span className="text-green-400 font-semibold">B.Sc in Computer Science and Engineering</span> at
                  BRAC University, I'm working on my thesis: <span className="text-blue-400 italic">"Enhancing Crop Production through a Novel Agricultural
                  Tool Incorporating Real-Time Weather and Crop Health Diagnostics."</span>
                </p>
                <p className="text-lg">
                  As the <span className="text-purple-400 font-semibold">Founder and CEO of dotPY Academy</span> and
                  <span className="text-yellow-400 font-semibold"> Assistant Director of the Robotics
                  Club at BRAC University</span>, I'm passionate about sharing knowledge and fostering
                  innovation in the next generation of engineers.
                </p>
                <p className="text-lg">
                  My work spans from <span className="text-red-400 font-semibold">rescue robotics</span> and
                  <span className="text-cyan-400 font-semibold"> autonomous vehicles</span> to
                  <span className="text-green-400 font-semibold"> agricultural AI</span> and
                  <span className="text-pink-400 font-semibold"> Bengali voice assistants</span>, always driven by the belief that
                  <span className="gradient-text font-semibold italic"> imagination is more important than innovation</span>.
                </p>
              </div>
            </div>

            {/* Education */}
            <div className="glass-effect rounded-xl p-8 border-l-4 border-purple-500">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center mr-4">
                  <GraduationCap className="text-white" size={24} />
                </div>
                <h3 className="text-2xl font-bold gradient-text">Education</h3>
              </div>
              <div className="space-y-6">
                <motion.div
                  className="relative p-4 rounded-lg bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-green-400 to-emerald-500 rounded-full"></div>
                  <div className="ml-4">
                    <h4 className="font-bold text-green-400 text-lg">B.Sc in Computer Science and Engineering</h4>
                    <p className="text-gray-300 font-medium">BRAC University</p>
                    <p className="text-gray-400 text-sm">May 2021 - Current</p>
                    <p className="text-blue-300 text-sm mt-2 italic">Thesis: Agricultural AI Tool for Crop Health & Weather Forecasting</p>
                  </div>
                </motion.div>

                <motion.div
                  className="relative p-4 rounded-lg bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-400 to-cyan-500 rounded-full"></div>
                  <div className="ml-4">
                    <h4 className="font-bold text-blue-400 text-lg">HSC in Science</h4>
                    <p className="text-gray-300 font-medium">Govt Yasin College</p>
                    <p className="text-gray-400 text-sm">April 2018 - April 2020</p>
                  </div>
                </motion.div>

                <motion.div
                  className="relative p-4 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-purple-400 to-pink-500 rounded-full"></div>
                  <div className="ml-4">
                    <h4 className="font-bold text-purple-400 text-lg">SSC in Science</h4>
                    <p className="text-gray-300 font-medium">Angaria High School</p>
                    <p className="text-gray-400 text-sm">January 2016 - February 2018</p>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Achievements & Strengths Sidebar */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Key Achievements */}
            <div className="glass-effect rounded-xl p-6 border-l-4 border-yellow-500">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-yellow-400 to-orange-400 flex items-center justify-center mr-3">
                  <Award className="text-white" size={20} />
                </div>
                <h3 className="text-xl font-bold gradient-text">Achievements</h3>
              </div>
              <div className="space-y-4">
                {achievements.map((achievement, index) => {
                  const Icon = achievement.icon
                  return (
                    <motion.div
                      key={index}
                      className="relative p-3 rounded-lg bg-gradient-to-r from-gray-800/50 to-gray-700/30 border border-gray-600/30 hover:border-gray-500/50 transition-all duration-300"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.02, y: -2 }}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg bg-gradient-to-r ${
                          index === 0 ? 'from-yellow-500 to-orange-500' :
                          index === 1 ? 'from-blue-500 to-cyan-500' :
                          index === 2 ? 'from-green-500 to-emerald-500' :
                          'from-purple-500 to-pink-500'
                        }`}>
                          <Icon className="text-white" size={16} />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-bold text-white text-sm mb-1">{achievement.title}</h4>
                          <p className="text-gray-300 text-xs leading-relaxed">{achievement.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </div>

            {/* Strengths */}
            <div className="glass-effect rounded-xl p-6 border-l-4 border-green-500">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-green-400 to-emerald-400 flex items-center justify-center mr-3">
                  <Zap className="text-white" size={20} />
                </div>
                <h3 className="text-xl font-bold gradient-text">Core Strengths</h3>
              </div>
              <div className="grid grid-cols-1 gap-3">
                {strengths.map((strength, index) => {
                  const Icon = strength.icon
                  return (
                    <motion.div
                      key={index}
                      className="flex items-center p-3 rounded-lg bg-gradient-to-r from-gray-800/40 to-gray-700/20 border border-gray-600/20 hover:border-green-400/30 transition-all duration-300"
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.02, x: 5 }}
                    >
                      <div className={`p-2 rounded-lg mr-3 bg-gradient-to-r ${
                        index === 0 ? 'from-red-500 to-pink-500' :
                        index === 1 ? 'from-blue-500 to-indigo-500' :
                        index === 2 ? 'from-green-500 to-teal-500' :
                        'from-purple-500 to-violet-500'
                      }`}>
                        <Icon className="text-white" size={16} />
                      </div>
                      <span className="text-sm font-medium text-gray-200">{strength.name}</span>
                    </motion.div>
                  )
                })}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Daily Life Visualization */}
        <motion.div
          className="glass-effect rounded-xl p-8 border-l-4 border-cyan-500"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center justify-center mb-8">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-cyan-400 to-blue-400 flex items-center justify-center mr-4">
              <Clock className="text-white" size={24} />
            </div>
            <h3 className="text-3xl font-bold gradient-text">A Day in My Life</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {dailyLife.map((item, index) => (
              <motion.div
                key={index}
                className="text-center group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="relative mb-6">
                  {/* Outer Ring */}
                  <div className="w-24 h-24 mx-auto rounded-full bg-gray-800 flex items-center justify-center relative overflow-hidden group-hover:shadow-lg transition-all duration-300">
                    {/* Background Circle */}
                    <div className={`absolute inset-2 rounded-full ${item.color} opacity-20`} />

                    {/* Progress Circle */}
                    <svg className="absolute inset-0 w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                      <circle
                        cx="50"
                        cy="50"
                        r="45"
                        fill="none"
                        stroke="rgba(255,255,255,0.1)"
                        strokeWidth="3"
                      />
                      <motion.circle
                        cx="50"
                        cy="50"
                        r="45"
                        fill="none"
                        stroke={`url(#gradient-${index})`}
                        strokeWidth="3"
                        strokeDasharray="283"
                        strokeDashoffset="283"
                        animate={{
                          strokeDashoffset: 283 - (283 * item.hours) / 24,
                        }}
                        transition={{ duration: 1.5, delay: index * 0.2 }}
                        strokeLinecap="round"
                      />
                      <defs>
                        <linearGradient id={`gradient-${index}`} x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor={
                            index === 0 ? '#3B82F6' :
                            index === 1 ? '#10B981' :
                            index === 2 ? '#8B5CF6' :
                            index === 3 ? '#F59E0B' :
                            '#EC4899'
                          } />
                          <stop offset="100%" stopColor={
                            index === 0 ? '#1D4ED8' :
                            index === 1 ? '#059669' :
                            index === 2 ? '#7C3AED' :
                            index === 3 ? '#D97706' :
                            '#BE185D'
                          } />
                        </linearGradient>
                      </defs>
                    </svg>

                    {/* Center Content */}
                    <div className="relative z-10 flex flex-col items-center">
                      <span className="text-white font-bold text-lg">{item.hours}</span>
                      <span className="text-gray-300 text-xs">hours</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-bold text-white text-lg group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-cyan-400 group-hover:to-blue-400 transition-all duration-300">
                    {item.activity}
                  </h4>
                  <div className="flex items-center justify-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                    <p className="text-gray-400 text-sm">{Math.round((item.hours / 24) * 100)}% of day</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Summary Stats */}
          <motion.div
            className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="text-center p-4 rounded-lg bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20">
              <div className="text-2xl font-bold text-blue-400">33%</div>
              <div className="text-sm text-gray-300">Productivity Focus</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20">
              <div className="text-2xl font-bold text-green-400">24/7</div>
              <div className="text-sm text-gray-300">Learning Mindset</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20">
              <div className="text-2xl font-bold text-purple-400">100%</div>
              <div className="text-sm text-gray-300">Passion Driven</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

export default About
