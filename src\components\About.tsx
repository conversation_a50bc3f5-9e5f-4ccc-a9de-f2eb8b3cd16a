import React from 'react'
import { motion } from 'framer-motion'
import { GraduationCap, Award, Users, Clock, Heart, Zap } from 'lucide-react'

const About: React.FC = () => {
  const achievements = [
    {
      icon: Award,
      title: "National Champion",
      description: "OVIJAN V2 - 1st place in Technoxian Bangladesh 2024",
      color: "text-yellow-400"
    },
    {
      icon: Award,
      title: "Global 6th Place",
      description: "TechnoxianWorld Robotics Championship",
      color: "text-blue-400"
    },
    {
      icon: GraduationCap,
      title: "AI Internship",
      description: "Selected for CodSoft AI Internship Program",
      color: "text-green-400"
    },
    {
      icon: Users,
      title: "Leadership Role",
      description: "Assistant Director - BRAC University Robotics Club",
      color: "text-purple-400"
    }
  ]

  const dailyLife = [
    { activity: "Sleep", hours: 5, color: "bg-blue-500" },
    { activity: "Class", hours: 4, color: "bg-green-500" },
    { activity: "Productivity & Research", hours: 8, color: "bg-purple-500" },
    { activity: "Sports & Relaxation", hours: 3, color: "bg-yellow-500" },
    { activity: "Family Time", hours: 4, color: "bg-pink-500" }
  ]

  const strengths = [
    { name: "Hard-working", icon: Zap },
    { name: "Critical Thinking", icon: Heart },
    { name: "Motivator & Leader", icon: Users },
    { name: "Research & Productivity", icon: GraduationCap }
  ]

  return (
    <div className="py-20 px-6">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-5xl md:text-6xl font-black tech-font mb-6">
            About <span className="gradient-text">Me</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            I'm a passionate AI/ML engineer and robotics innovator from Bangladesh,
            specializing in Python development and intelligent systems. I love building
            robots that can think and solve real-world problems.
          </p>
        </motion.div>

        {/* Main Content - Simplified Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Personal Story */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Journey Card */}
            <div className="glass-effect rounded-2xl p-8 border-l-4 border-green-400">
              <div className="flex items-center mb-6">
                <div className="w-14 h-14 rounded-xl bg-gradient-to-r from-green-400 to-blue-400 flex items-center justify-center mr-4">
                  <GraduationCap className="text-white" size={28} />
                </div>
                <h3 className="text-3xl font-bold gradient-text tech-font">My Journey</h3>
              </div>
              <div className="space-y-6 text-gray-300 leading-relaxed">
                <p className="text-lg">
                  🎓 Currently pursuing <span className="text-green-400 font-bold">Computer Science & Engineering</span> at
                  BRAC University. My thesis focuses on <span className="text-blue-400 font-semibold">AI-powered agricultural solutions</span>
                  for crop health monitoring and weather prediction.
                </p>
                <p className="text-lg">
                  🚀 As <span className="text-purple-400 font-bold">Founder & CEO of dotPY Academy</span> and
                  <span className="text-yellow-400 font-bold"> Assistant Director of BRAC Robotics Club</span>,
                  I'm building the next generation of tech innovators.
                </p>
                <p className="text-lg">
                  🤖 My expertise spans <span className="text-red-400 font-bold">rescue robotics</span>,
                  <span className="text-cyan-400 font-bold"> autonomous vehicles</span>,
                  <span className="text-green-400 font-bold"> agricultural AI</span>, and
                  <span className="text-pink-400 font-bold"> NLP systems</span>. I specialize in
                  <span className="gradient-text font-bold"> Python development</span> and machine learning.
                </p>
              </div>
            </div>

            {/* Education */}
            <div className="glass-effect rounded-xl p-8 border-l-4 border-purple-500">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center mr-4">
                  <GraduationCap className="text-white" size={24} />
                </div>
                <h3 className="text-2xl font-bold gradient-text">Education</h3>
              </div>
              <div className="space-y-6">
                <motion.div
                  className="relative p-4 rounded-lg bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-green-400 to-emerald-500 rounded-full"></div>
                  <div className="ml-4">
                    <h4 className="font-bold text-green-400 text-lg">B.Sc in Computer Science and Engineering</h4>
                    <p className="text-gray-300 font-medium">BRAC University</p>
                    <p className="text-gray-400 text-sm">May 2021 - Current</p>
                    <p className="text-blue-300 text-sm mt-2 italic">Thesis: Agricultural AI Tool for Crop Health & Weather Forecasting</p>
                  </div>
                </motion.div>

                <motion.div
                  className="relative p-4 rounded-lg bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/20"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-400 to-cyan-500 rounded-full"></div>
                  <div className="ml-4">
                    <h4 className="font-bold text-blue-400 text-lg">HSC in Science</h4>
                    <p className="text-gray-300 font-medium">Govt Yasin College</p>
                    <p className="text-gray-400 text-sm">April 2018 - April 2020</p>
                  </div>
                </motion.div>

                <motion.div
                  className="relative p-4 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-purple-400 to-pink-500 rounded-full"></div>
                  <div className="ml-4">
                    <h4 className="font-bold text-purple-400 text-lg">SSC in Science</h4>
                    <p className="text-gray-300 font-medium">Angaria High School</p>
                    <p className="text-gray-400 text-sm">January 2016 - February 2018</p>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Achievements & Skills */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Key Achievements */}
            <div className="glass-effect rounded-2xl p-8 border-l-4 border-yellow-400">
              <div className="flex items-center mb-6">
                <div className="w-14 h-14 rounded-xl bg-gradient-to-r from-yellow-400 to-orange-400 flex items-center justify-center mr-4">
                  <Award className="text-white" size={28} />
                </div>
                <h3 className="text-3xl font-bold gradient-text tech-font">Achievements</h3>
              </div>
              <div className="grid grid-cols-1 gap-4">
                {achievements.map((achievement, index) => {
                  const Icon = achievement.icon
                  return (
                    <motion.div
                      key={index}
                      className="p-4 rounded-xl bg-gradient-to-r from-gray-800/60 to-gray-700/40 border border-gray-600/40 hover:border-gray-500/60 transition-all duration-300"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.02, y: -2 }}
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`p-3 rounded-xl bg-gradient-to-r ${
                          index === 0 ? 'from-yellow-500 to-orange-500' :
                          index === 1 ? 'from-blue-500 to-cyan-500' :
                          index === 2 ? 'from-green-500 to-emerald-500' :
                          'from-purple-500 to-pink-500'
                        }`}>
                          <Icon className="text-white" size={20} />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-bold text-white text-lg mb-1">{achievement.title}</h4>
                          <p className="text-gray-300 text-sm">{achievement.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </div>

            {/* Core Skills */}
            <div className="glass-effect rounded-2xl p-8 border-l-4 border-green-400">
              <div className="flex items-center mb-6">
                <div className="w-14 h-14 rounded-xl bg-gradient-to-r from-green-400 to-emerald-400 flex items-center justify-center mr-4">
                  <Zap className="text-white" size={28} />
                </div>
                <h3 className="text-3xl font-bold gradient-text tech-font">Core Skills</h3>
              </div>
              <div className="grid grid-cols-1 gap-4">
                <div className="p-4 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30">
                  <div className="text-lg font-bold text-blue-400 mb-2">🐍 Python Expert</div>
                  <div className="text-sm text-gray-300">Advanced Python development, ML libraries, automation</div>
                </div>
                <div className="p-4 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30">
                  <div className="text-lg font-bold text-green-400 mb-2">🤖 AI/ML Engineering</div>
                  <div className="text-sm text-gray-300">TensorFlow, PyTorch, Computer Vision, NLP</div>
                </div>
                <div className="p-4 rounded-xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30">
                  <div className="text-lg font-bold text-purple-400 mb-2">⚙️ Robotics Systems</div>
                  <div className="text-sm text-gray-300">Arduino, Embedded Systems, Sensor Integration</div>
                </div>
                <div className="p-4 rounded-xl bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30">
                  <div className="text-lg font-bold text-yellow-400 mb-2">💡 Innovation & Leadership</div>
                  <div className="text-sm text-gray-300">Team leadership, Research, Problem solving</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Philosophy & Contact */}
        <motion.div
          className="text-center glass-effect rounded-2xl p-12 border-l-4 border-cyan-400"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="max-w-4xl mx-auto space-y-8">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 rounded-xl bg-gradient-to-r from-cyan-400 to-blue-400 flex items-center justify-center mr-4">
                <Heart className="text-white" size={32} />
              </div>
              <h3 className="text-4xl font-bold gradient-text tech-font">My Philosophy</h3>
            </div>

            <blockquote className="text-3xl md:text-4xl font-light italic text-gray-300 leading-relaxed">
              "Imagination is more important than innovation"
            </blockquote>

            <p className="text-xl text-gray-400 leading-relaxed max-w-3xl mx-auto">
              I believe in pushing the boundaries of what's possible through creative thinking and
              innovative solutions. Every robot I build, every AI model I train, and every line of
              Python code I write is driven by the desire to make the world a better place.
            </p>

            {/* Quick Contact */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <div className="p-6 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30">
                <div className="text-2xl mb-2">📧</div>
                <div className="text-sm text-gray-300">Email</div>
                <div className="text-green-400 font-semibold"><EMAIL></div>
              </div>
              <div className="p-6 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30">
                <div className="text-2xl mb-2">📱</div>
                <div className="text-sm text-gray-300">Phone</div>
                <div className="text-blue-400 font-semibold">+8801770065234</div>
              </div>
              <div className="p-6 rounded-xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30">
                <div className="text-2xl mb-2">📍</div>
                <div className="text-sm text-gray-300">Location</div>
                <div className="text-purple-400 font-semibold">Shariatpur, Bangladesh</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default About
