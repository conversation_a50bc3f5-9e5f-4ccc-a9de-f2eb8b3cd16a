import React from 'react'
import { motion } from 'framer-motion'
import { GraduationCap, Award, Users, Clock, Heart, Zap } from 'lucide-react'

const About: React.FC = () => {
  const achievements = [
    {
      icon: Award,
      title: "National Champion",
      description: "OVIJAN V2 - 1st place in Technoxian Bangladesh National Round 2024",
      color: "text-yellow-400"
    },
    {
      icon: Award,
      title: "Global Recognition",
      description: "6th place globally at TechnoxianWorld Robotics Championship",
      color: "text-blue-400"
    },
    {
      icon: GraduationCap,
      title: "AI Internship",
      description: "Selected for 4-week AI internship at CodSoft",
      color: "text-green-400"
    },
    {
      icon: Users,
      title: "Leadership",
      description: "Assistant Director at ROBOTICS CLUB OF BRAC UNIVERSITY",
      color: "text-purple-400"
    }
  ]

  const dailyLife = [
    { activity: "Sleep", hours: 5, color: "bg-blue-500" },
    { activity: "Class", hours: 4, color: "bg-green-500" },
    { activity: "Productivity & Research", hours: 8, color: "bg-purple-500" },
    { activity: "Sports & Relaxation", hours: 3, color: "bg-yellow-500" },
    { activity: "Family Time", hours: 4, color: "bg-pink-500" }
  ]

  const strengths = [
    { name: "Hard-working", icon: Zap },
    { name: "Critical Thinking", icon: Heart },
    { name: "Motivator & Leader", icon: Users },
    { name: "Research & Productivity", icon: GraduationCap }
  ]

  return (
    <div className="min-h-screen py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            About <span className="gradient-text">Me</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            A passionate AI/ML engineer and robotics innovator from Bangladesh, 
            dedicated to pushing the boundaries of technology and innovation.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Personal Story */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="glass-effect rounded-xl p-8">
              <h3 className="text-2xl font-bold mb-4 gradient-text">My Journey</h3>
              <div className="space-y-4 text-gray-300">
                <p>
                  Currently pursuing B.Sc in Computer Science and Engineering at BRAC University, 
                  I'm working on my thesis: "Enhancing Crop Production through a Novel Agricultural 
                  Tool Incorporating Real-Time Weather and Crop Health Diagnostics."
                </p>
                <p>
                  As the Founder and CEO of dotPY Academy and Assistant Director of the Robotics 
                  Club at BRAC University, I'm passionate about sharing knowledge and fostering 
                  innovation in the next generation of engineers.
                </p>
                <p>
                  My work spans from rescue robotics and autonomous vehicles to agricultural AI 
                  and Bengali voice assistants, always driven by the belief that imagination 
                  is more important than innovation.
                </p>
              </div>
            </div>

            {/* Education */}
            <div className="glass-effect rounded-xl p-8">
              <h3 className="text-2xl font-bold mb-4 gradient-text">Education</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-green-400 pl-4">
                  <h4 className="font-semibold text-white">B.Sc in Computer Science and Engineering</h4>
                  <p className="text-gray-300">BRAC University (May 2021 - Current)</p>
                </div>
                <div className="border-l-4 border-blue-400 pl-4">
                  <h4 className="font-semibold text-white">HSC in Science</h4>
                  <p className="text-gray-300">Govt Yasin College (April 2018 - April 2020)</p>
                </div>
                <div className="border-l-4 border-purple-400 pl-4">
                  <h4 className="font-semibold text-white">SSC in Science</h4>
                  <p className="text-gray-300">Angaria High School (January 2016 - February 2018)</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Achievements */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="glass-effect rounded-xl p-8">
              <h3 className="text-2xl font-bold mb-6 gradient-text">Key Achievements</h3>
              <div className="space-y-4">
                {achievements.map((achievement, index) => {
                  const Icon = achievement.icon
                  return (
                    <motion.div
                      key={index}
                      className="flex items-start space-x-4 p-4 rounded-lg hover:bg-white/5 transition-colors duration-300"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Icon className={`${achievement.color} mt-1`} size={24} />
                      <div>
                        <h4 className="font-semibold text-white mb-1">{achievement.title}</h4>
                        <p className="text-gray-300 text-sm">{achievement.description}</p>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </div>

            {/* Strengths */}
            <div className="glass-effect rounded-xl p-8">
              <h3 className="text-2xl font-bold mb-6 gradient-text">Core Strengths</h3>
              <div className="grid grid-cols-2 gap-4">
                {strengths.map((strength, index) => {
                  const Icon = strength.icon
                  return (
                    <motion.div
                      key={index}
                      className="flex flex-col items-center text-center p-4 rounded-lg hover:bg-white/5 transition-colors duration-300"
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Icon className="text-green-400 mb-2" size={24} />
                      <span className="text-sm text-gray-300">{strength.name}</span>
                    </motion.div>
                  )
                })}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Daily Life Visualization */}
        <motion.div
          className="glass-effect rounded-xl p-8"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold mb-6 text-center gradient-text">A Day in My Life</h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {dailyLife.map((item, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative mb-4">
                  <div className="w-20 h-20 mx-auto rounded-full bg-gray-800 flex items-center justify-center">
                    <Clock className="text-gray-400" size={24} />
                  </div>
                  <div className={`absolute inset-0 rounded-full ${item.color} opacity-20`} />
                  <div 
                    className={`absolute inset-0 rounded-full ${item.color}`}
                    style={{
                      clipPath: `polygon(50% 50%, 50% 0%, ${50 + (item.hours / 24) * 50}% 0%, ${50 + (item.hours / 24) * 50}% 100%, 50% 100%)`
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-white font-bold">{item.hours}h</span>
                  </div>
                </div>
                <h4 className="font-semibold text-white mb-1">{item.activity}</h4>
                <p className="text-gray-400 text-sm">{item.hours} hours</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default About
