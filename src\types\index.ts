export interface Project {
  id: string
  title: string
  category: string
  description: string
  longDescription: string
  technologies: string[]
  achievements?: string[]
  icon: React.ComponentType<any>
  color: string
  image?: string
}

export interface Skill {
  name: string
  level: number
  color: string
}

export interface SkillCategory {
  title: string
  icon: React.ComponentType<any>
  color: string
  skills: Skill[]
}

export interface ContactInfo {
  icon: React.ComponentType<any>
  label: string
  value: string
  href: string
  color: string
}

export interface Achievement {
  icon: React.ComponentType<any>
  title: string
  description: string
  color: string
}

export interface Language {
  name: string
  level: number
  flag: string
}

export interface Certification {
  title: string
  provider: string
  icon: React.ComponentType<any>
  color: string
}

export interface DailyActivity {
  activity: string
  hours: number
  color: string
}

export interface AIDemo {
  id: string
  title: string
  description: string
  icon: React.ComponentType<any>
  color: string
  category: string
  difficulty: string
}
