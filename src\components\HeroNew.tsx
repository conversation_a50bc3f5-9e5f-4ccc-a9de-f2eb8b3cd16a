import React, { useRef, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Points, PointMaterial, OrbitControls } from '@react-three/drei'
import { motion } from 'framer-motion'
import { ChevronDown } from 'lucide-react'
import * as THREE from 'three'

// Particle Field Component
const ParticleField: React.FC = () => {
  const ref = useRef<THREE.Points>(null!)
  const particlesPosition = useMemo(() => {
    const positions = new Float32Array(2000 * 3)
    for (let i = 0; i < 2000; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20
    }
    return positions
  }, [])

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.x = state.clock.elapsedTime * 0.05
      ref.current.rotation.y = state.clock.elapsedTime * 0.075
    }
  })

  return (
    <Points ref={ref} positions={particlesPosition} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color="#00ffff"
        size={0.05}
        sizeAttenuation={true}
        depthWrite={false}
      />
    </Points>
  )
}

// Floating Robot Component
const FloatingRobot: React.FC = () => {
  const meshRef = useRef<THREE.Mesh>(null!)

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.5
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.2
    }
  })

  return (
    <mesh ref={meshRef} position={[3, 0, 0]}>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color="#00ff88" wireframe />
    </mesh>
  )
}

const HeroNew: React.FC = () => {
  const scrollToNext = () => {
    const aboutSection = document.getElementById('about')
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="hero-section relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      {/* AI Neural Network Background */}
      <div className="absolute inset-0 z-0">
        <Canvas camera={{ position: [0, 0, 12], fov: 75 }}>
          <ambientLight intensity={0.4} />
          <pointLight position={[10, 10, 10]} intensity={1.5} color="#00ffff" />
          <pointLight position={[-10, -10, -10]} intensity={1} color="#ff00ff" />
          <spotLight position={[0, 20, 0]} intensity={0.8} color="#00ff00" />
          
          <ParticleField />
          <FloatingRobot />
          
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={0.5}
          />
        </Canvas>
      </div>

      {/* AI Grid Overlay */}
      <div className="absolute inset-0 z-5 opacity-10">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 w-full max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center min-h-screen py-20">
          
          {/* Left Side - Main Content */}
          <motion.div
            className="lg:col-span-7 space-y-8"
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
          >
            {/* AI Badge */}
            <motion.div
              className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 rounded-full backdrop-blur-sm"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="w-2 h-2 bg-cyan-400 rounded-full mr-3 animate-pulse" />
              <span className="text-cyan-300 text-sm font-medium">AI/ML Engineer & Robotics Innovator</span>
            </motion.div>

            {/* Main Title */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
            >
              <h1 className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black leading-tight">
                <span className="block text-white">POLOK</span>
                <span className="block bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                  PODDAR
                </span>
              </h1>
              
              {/* Typing Effect Subtitle */}
              <div className="text-xl md:text-2xl lg:text-3xl text-gray-300 font-light">
                <span className="text-cyan-400">{'>'}</span> Building the future with{' '}
                <span className="text-green-400 font-mono">Python</span>,{' '}
                <span className="text-blue-400 font-mono">AI</span> &{' '}
                <span className="text-purple-400 font-mono">Robotics</span>
                <span className="animate-pulse text-cyan-400">_</span>
              </div>
            </motion.div>

            {/* Achievement Highlight */}
            <motion.div
              className="flex items-center space-x-4 p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-xl backdrop-blur-sm"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <div className="text-3xl">🏆</div>
              <div>
                <div className="text-yellow-400 font-bold text-lg">National Champion</div>
                <div className="text-gray-300 text-sm">OVIJAN V2 - Global 6th Place</div>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1 }}
            >
              <motion.button
                className="px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-500 text-black font-bold rounded-xl text-lg hover:shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 flex items-center justify-center"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <span className="mr-2">🚀</span>
                Explore AI Projects
              </motion.button>
              <motion.button
                className="px-8 py-4 border-2 border-cyan-500/50 text-cyan-400 font-bold rounded-xl text-lg hover:bg-cyan-500/10 transition-all duration-300 flex items-center justify-center"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <span className="mr-2">💬</span>
                Let's Connect
              </motion.button>
            </motion.div>
          </motion.div>

          {/* Right Side - AI Stats Dashboard */}
          <motion.div
            className="lg:col-span-5 space-y-6"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 1.2 }}
          >
            {/* AI Stats Panel */}
            <div className="glass-effect rounded-2xl p-6 border border-cyan-500/30">
              <div className="flex items-center mb-4">
                <div className="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse" />
                <span className="text-green-400 font-mono text-sm">SYSTEM_STATUS: ONLINE</span>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-xl border border-blue-500/20">
                  <div className="text-2xl font-bold text-blue-400">95%</div>
                  <div className="text-xs text-gray-400">Python Mastery</div>
                </div>
                <div className="text-center p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-xl border border-green-500/20">
                  <div className="text-2xl font-bold text-green-400">12+</div>
                  <div className="text-xs text-gray-400">AI Projects</div>
                </div>
                <div className="text-center p-4 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-500/20">
                  <div className="text-2xl font-bold text-purple-400">6th</div>
                  <div className="text-xs text-gray-400">Global Rank</div>
                </div>
                <div className="text-center p-4 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-xl border border-yellow-500/20">
                  <div className="text-2xl font-bold text-yellow-400">3+</div>
                  <div className="text-xs text-gray-400">Years Exp</div>
                </div>
              </div>
            </div>

            {/* Tech Stack */}
            <div className="glass-effect rounded-2xl p-6 border border-purple-500/30">
              <h3 className="text-lg font-bold text-purple-400 mb-4 font-mono">TECH_STACK.exe</h3>
              <div className="space-y-3">
                {[
                  { name: 'Python', level: 95, color: 'from-blue-400 to-blue-600' },
                  { name: 'TensorFlow', level: 90, color: 'from-orange-400 to-orange-600' },
                  { name: 'PyTorch', level: 88, color: 'from-red-400 to-red-600' },
                  { name: 'OpenCV', level: 85, color: 'from-green-400 to-green-600' },
                ].map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    className="space-y-1"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 1.4 + index * 0.1 }}
                  >
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-300 font-mono">{skill.name}</span>
                      <span className="text-cyan-400">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-gray-800 rounded-full h-2">
                      <motion.div
                        className={`h-full bg-gradient-to-r ${skill.color} rounded-full`}
                        initial={{ width: 0 }}
                        animate={{ width: `${skill.level}%` }}
                        transition={{ duration: 1.5, delay: 1.6 + index * 0.1 }}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 2 }}
      >
        <motion.button
          onClick={scrollToNext}
          className="flex flex-col items-center text-gray-400 hover:text-white transition-colors duration-300 group"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <span className="text-sm mb-2 group-hover:text-green-400 transition-colors duration-300">
            Discover More
          </span>
          <ChevronDown size={28} className="group-hover:text-green-400 transition-colors duration-300" />
        </motion.button>
      </motion.div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/30 pointer-events-none" />
    </div>
  )
}

export default HeroNew
